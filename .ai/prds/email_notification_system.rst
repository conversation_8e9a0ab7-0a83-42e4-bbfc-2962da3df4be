Transactional Email Notification System with Noticed Gem
========================================================

**Open Questions**
------------------

1. Should email preferences be stored as JSON columns on the ``User`` model or separate boolean columns?
2. What should be the default frequency for job alert notifications (real-time or daily digest)?
3. Should we use Noticed's built-in delivery methods or customize for our existing ZeptoMail setup?

**Task Checklist**
------------------

**Phase 1: Noticed Gem Setup & Core Infrastructure**

☐ Add Noticed gem to Gemfile and run bundle install
☐ Generate Noticed configuration and initializer
☐ Create email preferences migration for User model
☐ Add email preference methods to User model
☐ Create base notification classes using Noticed
☐ Update existing email templates for Noticed compatibility
☐ Add email preference management to Scout/Talent settings
☐ Write unit tests for notification classes and preferences

**Phase 2: Job Lifecycle & Communication Notifications**

☐ Create job-related notification classes
☐ Implement message/communication notification classes
☐ Add notification triggers to Job and JobApplication models
☐ Add notification triggers to Message and Conversation models
☐ Create welcome notification for new users
☐ Implement profile completion reminder notifications
☐ Write unit tests for all notification workflows

**Phase 3: Settings Integration & Polish**

☐ Integrate email preferences into existing settings UI
☐ Apply stone color palette to preference forms
☐ Add notification delivery status tracking
☐ Implement notification cleanup/archival
☐ Add comprehensive error handling for failed deliveries
☐ Write integration tests for complete notification flows

**Phase 1: Noticed Gem Setup & Core Infrastructure**
----------------------------------------------------

**Affected Files:**
- ``Gemfile`` - Add noticed gem dependency
- ``config/initializers/noticed.rb`` - Noticed configuration
- ``db/migrate/xxx_add_email_preferences_to_users.rb`` - Add email preference columns
- ``app/models/user.rb`` - Add email preference methods and Noticed integration
- ``app/notifications/application_notification.rb`` - Base notification class
- ``app/notifications/job_notification.rb`` - Job-related notifications
- ``app/notifications/message_notification.rb`` - Message notifications
- ``app/views/noticed_mailer/`` - Email templates for notifications
- ``app/controllers/scout/settings_controller.rb`` - Add email preferences
- ``app/controllers/talent/settings_controller.rb`` - Add email preferences

**Noticed Gem Installation:**

Add to ``Gemfile``::

    gem 'noticed', '~> 1.6'

Run installation::

    bundle install
    rails generate noticed:install

**Database Schema Changes:**

Add email preference columns to ``users`` table::

    add_column :users, :email_job_notifications, :boolean, default: true
    add_column :users, :email_message_notifications, :boolean, default: true
    add_column :users, :email_account_notifications, :boolean, default: true

**User Model Extensions:**

Add email preference methods and Noticed integration to ``User`` model::

    # Email preference methods
    def email_notifications_enabled_for?(type)
      case type.to_sym
      when :job_notifications then email_job_notifications
      when :message_notifications then email_message_notifications
      when :account_notifications then email_account_notifications
      else false
      end
    end

    # Noticed integration
    has_many :notifications, as: :recipient, dependent: :destroy

**Base Notification Classes:**

Create ``ApplicationNotification`` base class::

    class ApplicationNotification < Noticed::Base
      deliver_by :email, mailer: 'NotificationMailer', if: :email_notifications_enabled?

      private

      def email_notifications_enabled?
        recipient.email_notifications_enabled_for?(notification_type)
      end

      def notification_type
        self.class.name.underscore.gsub('_notification', '').to_sym
      end
    end

**Job Notification Classes:**

Create ``JobNotification`` for job-related emails::

    class JobNotification < ApplicationNotification
      def job_posted_confirmation
        # Notify scout when job is posted
      end

      def application_received
        # Notify scout when application is received
      end

      def application_status_changed
        # Notify talent when application status changes
      end
    end

**Message Notification Classes:**

Create ``MessageNotification`` for communication alerts::

    class MessageNotification < ApplicationNotification
      def new_message
        # Notify recipient of new message
      end
    end

**Noticed Mailer Configuration:**

Create ``NotificationMailer`` to handle email delivery::

    class NotificationMailer < ApplicationMailer
      def job_posted_confirmation
      def application_received
      def application_status_changed
      def new_message
    end

**Settings Integration:**

Add email preferences to existing Scout settings::

    # In app/controllers/scout/settings_controller.rb
    def user_params
      params.require(:user).permit(
        :first_name, :last_name, :email, :time_zone, :avatar,
        :email_job_notifications, :email_message_notifications, :email_account_notifications
      )
    end

Add email preferences to existing Talent settings::

    # In app/controllers/talent/settings_controller.rb
    def user_params
      params.require(:user).permit(
        :first_name, :last_name, :email, :time_zone, :avatar,
        :email_job_notifications, :email_message_notifications, :email_account_notifications
      )
    end

**Unit Tests:**

- ``test/notifications/job_notification_test.rb`` - Test job notification delivery
- ``test/notifications/message_notification_test.rb`` - Test message notifications
- ``test/mailers/notification_mailer_test.rb`` - Test email template rendering
- ``test/models/user_test.rb`` - Test email preference methods
- ``test/controllers/scout/settings_controller_test.rb`` - Test scout preference updates
- ``test/controllers/talent/settings_controller_test.rb`` - Test talent preference updates

**Phase 2: Job Lifecycle & Communication Notifications**
--------------------------------------------------------

**Affected Files:**
- ``app/notifications/job_posted_notification.rb`` - Job posted confirmation
- ``app/notifications/application_received_notification.rb`` - Application notifications
- ``app/notifications/application_status_notification.rb`` - Status change notifications
- ``app/notifications/message_received_notification.rb`` - Message alerts
- ``app/notifications/welcome_notification.rb`` - Welcome emails
- ``app/notifications/profile_completion_notification.rb`` - Profile reminders
- ``app/models/job.rb`` - Add notification triggers
- ``app/models/job_application.rb`` - Add notification triggers
- ``app/models/message.rb`` - Add notification triggers
- ``app/models/user.rb`` - Add registration notification trigger

**Job Lifecycle Notifications:**

Create specific notification classes for job events::

    class JobPostedNotification < ApplicationNotification
      deliver_by :email, mailer: 'NotificationMailer', method: :job_posted_confirmation

      def message
        "Your job '#{params[:job].title}' has been posted successfully"
      end
    end

    class ApplicationReceivedNotification < ApplicationNotification
      deliver_by :email, mailer: 'NotificationMailer', method: :application_received

      def message
        "New application received for '#{params[:job].title}'"
      end
    end

    class ApplicationStatusNotification < ApplicationNotification
      deliver_by :email, mailer: 'NotificationMailer', method: :application_status_changed

      def message
        "Your application status has been updated to #{params[:status]}"
      end
    end

**Message Notifications:**

Create message notification class::

    class MessageReceivedNotification < ApplicationNotification
      deliver_by :email, mailer: 'NotificationMailer', method: :message_received

      def message
        "New message from #{params[:sender].full_name}"
      end
    end

**Model Integration:**

Add notification triggers to models::

    # In Job model
    after_create :send_job_posted_notification

    private

    def send_job_posted_notification
      JobPostedNotification.with(job: self).deliver(organization.primary_user)
    end

    # In JobApplication model
    after_create :notify_scout_of_application
    after_update :notify_talent_of_status_change, if: :saved_change_to_status?

    private

    def notify_scout_of_application
      ApplicationReceivedNotification.with(job: job, application: self).deliver(job.organization.primary_user)
    end

    def notify_talent_of_status_change
      ApplicationStatusNotification.with(application: self, status: status).deliver(user)
    end

**Unit Tests:**

- ``test/notifications/job_posted_notification_test.rb`` - Test job posted notifications
- ``test/notifications/application_received_notification_test.rb`` - Test application notifications
- ``test/notifications/message_received_notification_test.rb`` - Test message notifications
- ``test/models/job_test.rb`` - Test job notification triggers
- ``test/models/job_application_test.rb`` - Test application notification triggers

**Phase 3: Settings Integration & Polish**
------------------------------------------

**Affected Files:**
- ``app/views/scout/settings/show.html.erb`` - Add email preference section
- ``app/views/talent/settings/show.html.erb`` - Add email preference section
- ``app/views/shared/_email_preferences.html.erb`` - Shared preference form partial
- ``app/notifications/welcome_notification.rb`` - Welcome notification for new users
- ``app/notifications/profile_completion_notification.rb`` - Profile completion reminders
- ``app/jobs/cleanup_notifications_job.rb`` - Notification cleanup job
- ``config/schedule.rb`` - Add notification cleanup cron job

**Settings UI Integration:**

Add email preferences section to Scout settings using stone color palette::

    <!-- In app/views/scout/settings/show.html.erb -->
    <div class="p-6 mb-6 bg-white border rounded-lg border-stone-200">
      <h3 class="mb-4 text-lg font-medium text-stone-900">Email Notifications</h3>
      <%= render 'shared/email_preferences', user: @user %>
    </div>

Add email preferences section to Talent settings::

    <!-- In app/views/talent/settings/show.html.erb -->
    <div class="p-6 mb-6 bg-white border rounded-lg border-stone-200">
      <h3 class="mb-4 text-lg font-medium text-stone-900">Email Notifications</h3>
      <%= render 'shared/email_preferences', user: @user %>
    </div>

**Shared Email Preferences Partial:**

Create reusable form partial with stone color styling::

    <!-- In app/views/shared/_email_preferences.html.erb -->
    <%= form_with model: user, local: true, class: "space-y-4" do |form| %>
      <div class="space-y-3">
        <label class="flex items-center">
          <%= form.check_box :email_job_notifications, class: "rounded border-stone-300 text-stone-600 focus:ring-stone-500" %>
          <span class="ml-2 text-sm text-stone-700">Job notifications</span>
        </label>

        <label class="flex items-center">
          <%= form.check_box :email_message_notifications, class: "rounded border-stone-300 text-stone-600 focus:ring-stone-500" %>
          <span class="ml-2 text-sm text-stone-700">Message notifications</span>
        </label>

        <label class="flex items-center">
          <%= form.check_box :email_account_notifications, class: "rounded border-stone-300 text-stone-600 focus:ring-stone-500" %>
          <span class="ml-2 text-sm text-stone-700">Account notifications</span>
        </label>
      </div>
    <% end %>

**Welcome & Profile Notifications:**

Create welcome notification for new users::

    class WelcomeNotification < ApplicationNotification
      deliver_by :email, mailer: 'NotificationMailer', method: :welcome

      def message
        "Welcome to Ghostwrote! Get started by completing your profile."
      end
    end

Create profile completion reminder::

    class ProfileCompletionNotification < ApplicationNotification
      deliver_by :email, mailer: 'NotificationMailer', method: :profile_completion_reminder

      def message
        "Complete your profile to get the most out of Ghostwrote"
      end
    end

**Notification Cleanup:**

Create cleanup job for old notifications::

    class CleanupNotificationsJob < ApplicationJob
      def perform
        # Remove notifications older than 90 days
        Noticed::Notification.where('created_at < ?', 90.days.ago).delete_all
      end
    end

**Unit Tests:**

- ``test/notifications/welcome_notification_test.rb`` - Test welcome notifications
- ``test/notifications/profile_completion_notification_test.rb`` - Test profile reminders
- ``test/jobs/cleanup_notifications_job_test.rb`` - Test notification cleanup
- ``test/controllers/scout/settings_controller_test.rb`` - Test email preference updates
- ``test/controllers/talent/settings_controller_test.rb`` - Test email preference updates
