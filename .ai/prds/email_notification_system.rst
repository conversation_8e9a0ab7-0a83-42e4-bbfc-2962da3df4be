Comprehensive Email Notification System Implementation Plan
===========================================================

**Open Questions**
------------------

1. Should email preferences be stored as a separate ``EmailPreference`` model or as JSON columns on the ``User`` model?
2. What should be the default email frequency for job alerts (real-time, daily digest, or weekly)?
3. Should we implement email tracking (opens/clicks) immediately or defer to Phase 4?
4. How should we handle email bounces and unsubscribes for compliance?

**Task Checklist**
------------------

**Phase 1: Foundation & Core Infrastructure**

☐ Create email preferences migration and model
☐ Add email preference fields to User model
☐ Create base mailer classes (ScoutMailer, TalentMailer, JobMailer)
☐ Implement email preference management UI
☐ Create responsive email template layouts
☐ Add job lifecycle email triggers
☐ Implement message notification emails
☐ Write unit tests for mailers and preference logic

**Phase 2: Onboarding & User Engagement**

☐ Create welcome email series for scouts and talent
☐ Implement profile completion reminder emails
☐ Add job alert system for talent with personalization
☐ Create application status notification emails
☐ Implement behavioral trigger emails (cart abandonment)
☐ Add milestone celebration emails
☐ Write unit tests for onboarding and engagement flows

**Phase 3: Retention & Advanced Features**

☐ Create inactive user re-engagement campaigns
☐ Implement performance summary emails
☐ Add platform update and newsletter emails
☐ Create email analytics tracking system
☐ Implement A/B testing framework for emails
☐ Add comprehensive email delivery monitoring
☐ Write integration tests for complete email workflows

**Phase 1: Foundation & Core Infrastructure**
---------------------------------------------

**Affected Files:**
- ``db/migrate/xxx_add_email_preferences_to_users.rb`` - Add email preference columns
- ``app/models/user.rb`` - Add email preference methods and validations
- ``app/mailers/scout_mailer.rb`` - New scout-focused mailer
- ``app/mailers/talent_mailer.rb`` - New talent-focused mailer  
- ``app/mailers/job_mailer.rb`` - New job-related mailer
- ``app/views/layouts/mailer.html.erb`` - Update base email template
- ``app/controllers/settings/email_preferences_controller.rb`` - New preference management
- ``app/views/settings/email_preferences/`` - Email preference forms
- ``config/routes.rb`` - Add email preference routes

**Database Schema Changes:**

Add email preference columns to ``users`` table::

    add_column :users, :email_job_alerts, :boolean, default: true
    add_column :users, :email_job_alerts_frequency, :string, default: 'daily'
    add_column :users, :email_application_updates, :boolean, default: true
    add_column :users, :email_messages, :boolean, default: true
    add_column :users, :email_platform_updates, :boolean, default: true
    add_column :users, :email_marketing, :boolean, default: true
    add_column :users, :email_unsubscribed_at, :datetime

**User Model Extensions:**

Add email preference methods to ``User`` model::

    def email_preferences_for(category)
      case category
      when :job_alerts then email_job_alerts && !email_unsubscribed?
      when :application_updates then email_application_updates && !email_unsubscribed?
      when :messages then email_messages && !email_unsubscribed?
      when :platform_updates then email_platform_updates && !email_unsubscribed?
      when :marketing then email_marketing && !email_unsubscribed?
      end
    end

    def email_unsubscribed?
      email_unsubscribed_at.present?
    end

**New Mailer Classes:**

Create ``ScoutMailer`` for business-focused emails::

    class ScoutMailer < ApplicationMailer
      def job_posted_confirmation(scout, job)
      def first_application_received(scout, job, application)
      def application_milestone_reached(scout, job, application_count)
      def no_applications_reminder(scout, job)
      def job_expiring_soon(scout, job)
      def new_message_notification(scout, message)
      def project_completion_celebration(scout, job)

**Create ``TalentMailer`` for ghostwriter-focused emails:**

    class TalentMailer < ApplicationMailer
      def new_job_alert(talent, jobs)
      def application_status_update(talent, application, status)
      def saved_job_expiring(talent, job)
      def new_message_notification(talent, message)
      def milestone_achievement(talent, milestone_type, data)
      def weekly_performance_summary(talent, stats)

**Create ``JobMailer`` for job-specific notifications:**

    class JobMailer < ApplicationMailer
      def application_submitted(job, application)
      def job_status_changed(job, old_status, new_status)

**Email Template System:**

Update base mailer layout with responsive design and Ghostwrote branding. Include unsubscribe links and preference management.

**Email Preference Management:**

Create settings controller and views for users to manage email preferences with granular controls for each notification type and frequency settings.

**Job Lifecycle Integration:**

Add email triggers to existing job workflow::

    # In Job model
    after_create :send_job_posted_confirmation
    after_update :send_job_status_notifications, if: :saved_change_to_status?

    # In JobApplication model  
    after_create :send_application_notifications
    after_update :send_application_status_update, if: :saved_change_to_status?

**Unit Tests:**

- ``test/mailers/scout_mailer_test.rb`` - Test all scout email methods
- ``test/mailers/talent_mailer_test.rb`` - Test all talent email methods  
- ``test/mailers/job_mailer_test.rb`` - Test job notification methods
- ``test/models/user_test.rb`` - Test email preference methods
- ``test/controllers/settings/email_preferences_controller_test.rb`` - Test preference management

**Phase 2: Onboarding & User Engagement**
-----------------------------------------

**Affected Files:**
- ``app/mailers/onboarding_mailer.rb`` - New onboarding email sequences
- ``app/jobs/welcome_email_sequence_job.rb`` - Scheduled welcome emails
- ``app/jobs/job_alert_digest_job.rb`` - Daily/weekly job alert compilation
- ``app/jobs/profile_completion_reminder_job.rb`` - Profile completion nudges
- ``app/services/job_matching_service.rb`` - Job recommendation logic
- ``app/models/job.rb`` - Add job matching scopes
- ``app/models/talent_profile.rb`` - Add completion percentage logic
- ``config/schedule.rb`` - Add cron jobs for email campaigns

**Welcome Email Sequences:**

Create multi-part welcome series with delayed delivery::

    class OnboardingMailer < ApplicationMailer
      def scout_welcome_day_1(scout)
      def scout_welcome_day_3(scout) 
      def scout_welcome_day_7(scout)
      def talent_welcome_day_1(talent)
      def talent_welcome_day_3(talent)
      def talent_welcome_day_7(talent)

**Job Alert System:**

Implement personalized job matching and digest emails::

    class JobAlertDigestJob < ApplicationJob
      def perform(frequency = 'daily')
        User.joins(:talent_profile)
            .where(email_job_alerts: true, email_job_alerts_frequency: frequency)
            .find_each do |talent|
          matching_jobs = JobMatchingService.new(talent).matching_jobs
          TalentMailer.new_job_alert(talent, matching_jobs).deliver_now if matching_jobs.any?
        end
      end

**Profile Completion Tracking:**

Add completion percentage logic to models::

    # In TalentProfile
    def completion_percentage
      required_fields = [:bio, :skills, :price_range_min, :price_range_max]
      completed = required_fields.count { |field| self[field].present? }
      (completed.to_f / required_fields.length * 100).round
    end

**Behavioral Triggers:**

Implement cart abandonment and incomplete action emails using background jobs with delayed execution.

**Unit Tests:**

- ``test/mailers/onboarding_mailer_test.rb`` - Test welcome email sequences
- ``test/jobs/job_alert_digest_job_test.rb`` - Test job alert compilation
- ``test/services/job_matching_service_test.rb`` - Test job recommendation logic
- ``test/models/talent_profile_test.rb`` - Test completion percentage calculation

**Phase 3: Retention & Advanced Features**  
------------------------------------------

**Affected Files:**
- ``app/jobs/inactive_user_campaign_job.rb`` - Re-engagement campaigns
- ``app/mailers/engagement_mailer.rb`` - Platform-wide engagement emails
- ``app/models/email_tracking.rb`` - Email analytics tracking
- ``app/services/email_analytics_service.rb`` - Email performance metrics
- ``app/controllers/admin/email_analytics_controller.rb`` - Analytics dashboard
- ``app/jobs/monthly_newsletter_job.rb`` - Newsletter compilation and sending
- ``config/initializers/email_tracking.rb`` - Email tracking configuration

**Re-engagement Campaigns:**

Create targeted campaigns for inactive users::

    class InactiveUserCampaignJob < ApplicationJob
      def perform
        # Scout re-engagement (30 days inactive)
        User.joins(:organizations)
            .where('last_sign_in_at < ?', 30.days.ago)
            .where(email_marketing: true)
            .find_each do |scout|
          EngagementMailer.scout_reengagement(scout).deliver_now
        end

        # Talent re-engagement (14 days inactive)  
        User.joins(:talent_profile)
            .where('last_sign_in_at < ?', 14.days.ago)
            .where(email_marketing: true)
            .find_each do |talent|
          EngagementMailer.talent_reengagement(talent).deliver_now
        end
      end

**Email Analytics System:**

Create tracking model and service for email performance::

    class EmailTracking < ApplicationRecord
      belongs_to :user
      validates :email_type, :sent_at, presence: true
      
      scope :opened, -> { where.not(opened_at: nil) }
      scope :clicked, -> { where.not(clicked_at: nil) }
    end

**A/B Testing Framework:**

Implement simple A/B testing for email subject lines and content with variant tracking and performance comparison.

**Monthly Newsletter:**

Create automated newsletter compilation with platform updates, success stories, and user-generated content.

**Unit Tests:**

- ``test/jobs/inactive_user_campaign_job_test.rb`` - Test re-engagement logic
- ``test/models/email_tracking_test.rb`` - Test analytics tracking
- ``test/services/email_analytics_service_test.rb`` - Test performance metrics
- ``test/mailers/engagement_mailer_test.rb`` - Test platform-wide emails
